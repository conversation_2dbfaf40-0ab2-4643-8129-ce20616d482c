# Backend Configuration
# Set this to your computer's IP address for network access
# Examples:
# VITE_BACKEND_URL=http://*************:8000
# VITE_BACKEND_URL=http://*********:8000
# VITE_BACKEND_URL=http://localhost:8000

# Default: localhost (for local development)
VITE_BACKEND_URL=http://localhost:8000

# Backend Host (for <PERSON><PERSON> to start the Python service)
# Use 0.0.0.0 for network access, 127.0.0.1 for localhost only
BACKEND_HOST=0.0.0.0

# Backend Port
BACKEND_PORT=8000

# Full Backend URL (constructed from host and port)
BACKEND_URL=http://localhost:8000
