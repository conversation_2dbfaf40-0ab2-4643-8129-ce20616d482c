{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "dev:network": "vite --host 0.0.0.0 --port 5173", "dev:server": "vite --host 0.0.0.0 --port 5173 --cors", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "preview:network": "vite preview --host 0.0.0.0 --port 4173", "preview:server": "vite preview --host 0.0.0.0 --port 4173 --cors", "start:network": "bun run build && bun run preview:network"}, "dependencies": {"@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-switch": "^1.2.5", "@tailwindcss/typography": "^0.5.16", "@tauri-apps/api": "^2.7.0", "@tauri-apps/plugin-dialog": "^2.3.2", "@tauri-apps/plugin-fs": "^2.4.1", "@tauri-apps/plugin-shell": "^2.3.0", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.23.12", "lucide-react": "^0.537.0", "postcss": "^8.5.6", "react": "^19.1.1", "react-dom": "^19.1.1", "react-dropzone": "^14.3.8", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "zustand": "^5.0.7"}, "devDependencies": {"@eslint/js": "^9.33.0", "@tailwindcss/postcss": "^4.1.11", "@types/node": "^24.2.1", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^4.7.0", "eslint": "^9.33.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "typescript": "~5.8.3", "typescript-eslint": "^8.39.0", "vite": "^7.1.1"}}