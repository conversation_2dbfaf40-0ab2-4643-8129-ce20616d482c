{"$schema": "https://schema.tauri.app/config/2", "productName": "OCR Grammar Assistant", "version": "0.1.0", "identifier": "com.ocr-grammar-assistant.app", "build": {"frontendDist": "../frontend/dist", "devUrl": "http://localhost:5173", "beforeDevCommand": "", "beforeBuildCommand": "cd ../frontend && bun run build && python3 ../build_executable.py"}, "app": {"windows": [{"title": "OCR & Grammar Assistant", "width": 1200, "height": 800, "minWidth": 800, "minHeight": 600, "resizable": true, "fullscreen": false, "center": true}], "security": {"csp": null}}, "plugins": {}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"], "resources": [], "externalBin": []}}